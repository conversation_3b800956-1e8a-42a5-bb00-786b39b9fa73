#!/usr/bin/env python3
"""
Pruebas con CloudScraper v3.0.0 - Versión más avanzada
https://github.com/VeNoMouS/cloudscraper/releases/tag/3.0.0
"""

import sys
import os
import time
import random
import json
from typing import Optional, Dict, Any

# Agregar el módulo cloudscraper v3 al path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'cloudscraper_v3'))

import cloudscraper_v3 as cloudscraper

# URL de prueba
TEST_URL = "https://novelasligeras.net/index.php/producto/lord-of-mysteries-novela-ligera/"

def get_advanced_headers() -> Dict[str, str]:
    """Headers más avanzados y realistas para 2024"""
    return {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'es-ES,es;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Cache-Control': 'max-age=0',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'DNT': '1',
        'Connection': 'keep-alive'
    }

def test_cloudscraper_v3_basic(url: str) -> Optional[str]:
    """Prueba básica con CloudScraper v3.0.0"""
    print("🧪 Probando CloudScraper v3.0.0 básico...")
    try:
        scraper = cloudscraper.create_scraper()
        print(f"   Versión: {cloudscraper.__version__ if hasattr(cloudscraper, '__version__') else '3.0.0'}")
        
        response = scraper.get(url, timeout=30)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Éxito con CloudScraper v3 básico!")
            return response.text
        else:
            print(f"   ❌ Error HTTP: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    return None

def test_cloudscraper_v3_stealth(url: str) -> Optional[str]:
    """Prueba con modo stealth de CloudScraper v3.0.0"""
    print("🧪 Probando CloudScraper v3.0.0 con modo stealth...")
    try:
        # Usar el modo stealth si está disponible
        scraper = cloudscraper.create_scraper(
            browser={
                'browser': 'chrome',
                'platform': 'windows',
                'desktop': True
            }
        )
        
        # Configurar headers avanzados
        headers = get_advanced_headers()
        
        # Intentar con stealth mode si está disponible
        if hasattr(scraper, 'stealth'):
            print("   Activando modo stealth...")
            scraper.stealth = True
        
        response = scraper.get(url, headers=headers, timeout=45)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Éxito con CloudScraper v3 stealth!")
            return response.text
        else:
            print(f"   ❌ Error HTTP: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    return None

def test_cloudscraper_v3_advanced_config(url: str) -> Optional[str]:
    """Prueba con configuración avanzada de CloudScraper v3.0.0"""
    print("🧪 Probando CloudScraper v3.0.0 con configuración avanzada...")
    
    advanced_configs = [
        {
            'browser': {
                'browser': 'chrome',
                'platform': 'windows',
                'desktop': True
            },
            'interpreter': 'js2py',
            'debug': False
        },
        {
            'browser': {
                'browser': 'chrome',
                'platform': 'darwin',
                'desktop': True
            },
            'interpreter': 'native',
            'debug': False
        },
        {
            'browser': {
                'browser': 'firefox',
                'platform': 'windows',
                'desktop': True
            },
            'interpreter': 'js2py',
            'debug': False
        }
    ]
    
    for i, config in enumerate(advanced_configs):
        try:
            print(f"   Configuración {i+1}: {config['browser']['browser']} en {config['browser']['platform']}")
            
            # Crear scraper con configuración específica
            scraper = cloudscraper.create_scraper(**config)
            
            headers = get_advanced_headers()
            
            # Agregar delay aleatorio
            delay = random.uniform(2, 5)
            print(f"   Esperando {delay:.1f}s antes de la solicitud...")
            time.sleep(delay)
            
            response = scraper.get(url, headers=headers, timeout=60)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ Éxito con configuración {i+1}!")
                return response.text
            else:
                print(f"   ❌ Error HTTP: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error con configuración {i+1}: {e}")
        
        # Pausa entre configuraciones
        time.sleep(random.uniform(3, 6))
    
    return None

def test_cloudscraper_v3_with_session(url: str) -> Optional[str]:
    """Prueba con sesión persistente"""
    print("🧪 Probando CloudScraper v3.0.0 con sesión persistente...")
    
    try:
        # Crear scraper con sesión
        scraper = cloudscraper.create_scraper(
            browser={
                'browser': 'chrome',
                'platform': 'windows',
                'desktop': True
            }
        )
        
        # Primero visitar la página principal
        base_url = "https://novelasligeras.net/"
        print(f"   Estableciendo sesión en: {base_url}")
        
        headers = get_advanced_headers()
        
        # Visita inicial
        base_response = scraper.get(base_url, headers=headers, timeout=30)
        print(f"   Status página base: {base_response.status_code}")
        
        if base_response.status_code == 200:
            print("   ✅ Sesión establecida")
            
            # Esperar un poco
            wait_time = random.uniform(3, 7)
            print(f"   Esperando {wait_time:.1f}s...")
            time.sleep(wait_time)
            
            # Ahora intentar la página objetivo
            headers['Referer'] = base_url
            response = scraper.get(url, headers=headers, timeout=45)
            print(f"   Status página objetivo: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ Éxito con sesión persistente!")
                return response.text
            else:
                print(f"   ❌ Error HTTP: {response.status_code}")
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    return None

def test_cloudscraper_v3_turnstile(url: str) -> Optional[str]:
    """Prueba con soporte para Turnstile (Cloudflare v3)"""
    print("🧪 Probando CloudScraper v3.0.0 con soporte Turnstile...")
    
    try:
        # Configuración específica para Turnstile
        scraper = cloudscraper.create_scraper(
            browser={
                'browser': 'chrome',
                'platform': 'windows',
                'desktop': True
            },
            interpreter='js2py'  # Mejor soporte para Turnstile
        )
        
        headers = get_advanced_headers()
        
        # Configurar para manejar Turnstile si está presente
        response = scraper.get(url, headers=headers, timeout=60)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Éxito con soporte Turnstile!")
            return response.text
        else:
            print(f"   ❌ Error HTTP: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    return None

def analyze_content(html_content: str) -> Dict[str, Any]:
    """Analiza el contenido HTML obtenido"""
    if not html_content:
        return {"success": False, "reason": "No content"}
    
    analysis = {
        "success": True,
        "length": len(html_content),
        "contains_title": "lord of mysteries" in html_content.lower(),
        "contains_product": "producto" in html_content.lower(),
        "contains_woocommerce": "woocommerce" in html_content.lower(),
        "contains_cloudflare": "cloudflare" in html_content.lower(),
        "contains_403": "403" in html_content,
        "contains_forbidden": "forbidden" in html_content.lower(),
        "contains_challenge": "challenge" in html_content.lower(),
        "contains_turnstile": "turnstile" in html_content.lower(),
        "contains_javascript": "<script" in html_content.lower()
    }
    
    return analysis

def main():
    """Función principal para probar CloudScraper v3.0.0"""
    print("🚀 Iniciando pruebas de CloudScraper v3.0.0")
    print(f"🎯 URL objetivo: {TEST_URL}")
    print("=" * 80)
    
    strategies = [
        ("CloudScraper v3 Básico", test_cloudscraper_v3_basic),
        ("CloudScraper v3 Stealth", test_cloudscraper_v3_stealth),
        ("CloudScraper v3 Configuración Avanzada", test_cloudscraper_v3_advanced_config),
        ("CloudScraper v3 Sesión Persistente", test_cloudscraper_v3_with_session),
        ("CloudScraper v3 Turnstile", test_cloudscraper_v3_turnstile)
    ]
    
    results = {}
    successful_strategies = []
    
    for strategy_name, strategy_func in strategies:
        print(f"\n📋 Estrategia: {strategy_name}")
        print("-" * 50)
        
        try:
            html_content = strategy_func(TEST_URL)
            analysis = analyze_content(html_content)
            results[strategy_name] = analysis
            
            if analysis.get("success"):
                print(f"   📊 Longitud: {analysis['length']} caracteres")
                print(f"   🎯 Contiene título: {analysis['contains_title']}")
                print(f"   🛒 Contiene producto: {analysis['contains_product']}")
                print(f"   ⚡ WooCommerce: {analysis['contains_woocommerce']}")
                print(f"   🛡️ Cloudflare: {analysis['contains_cloudflare']}")
                print(f"   🔄 Turnstile: {analysis['contains_turnstile']}")
                print(f"   🚫 Error 403: {analysis['contains_403']}")
                
                if analysis['contains_title'] and analysis['contains_product']:
                    print(f"   🎉 ¡ESTRATEGIA EXITOSA! {strategy_name}")
                    successful_strategies.append(strategy_name)
                    
                    # Guardar muestra del HTML exitoso
                    filename = f"sample_v3_{strategy_name.lower().replace(' ', '_').replace('cloudscraper_v3_', '')}.html"
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(html_content[:15000])  # Primeros 15000 caracteres
                    print(f"   💾 Muestra guardada en {filename}")
            
        except Exception as e:
            print(f"   💥 Error crítico: {e}")
            results[strategy_name] = {"success": False, "error": str(e)}
        
        # Pausa entre estrategias
        time.sleep(random.uniform(4, 8))
    
    # Resumen final
    print("\n" + "=" * 80)
    print("📈 RESUMEN DE RESULTADOS CLOUDSCRAPER v3.0.0")
    print("=" * 80)
    
    for strategy, result in results.items():
        if result.get("success") and result.get("contains_title"):
            status = "✅ ÉXITO COMPLETO"
        elif result.get("success"):
            status = "⚠️ PARCIAL"
        else:
            status = "❌ FALLÓ"
        
        print(f"{strategy:45} | {status}")
    
    if successful_strategies:
        print(f"\n🎯 Estrategias completamente exitosas: {len(successful_strategies)}")
        for strategy in successful_strategies:
            print(f"   ✅ {strategy}")
        print("\n💡 Recomendación: CloudScraper v3.0.0 parece ser más efectivo!")
        print("💡 Implementar la estrategia exitosa en el código principal")
    else:
        print("\n😞 CloudScraper v3.0.0 tampoco fue exitoso")
        print("💡 El sitio puede tener protecciones muy avanzadas")
        print("💡 Considerar usar Playwright o descarga manual")
    
    # Guardar resultados
    with open("cloudscraper_v3_results.json", 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    print(f"\n💾 Resultados guardados en cloudscraper_v3_results.json")

if __name__ == "__main__":
    main()
