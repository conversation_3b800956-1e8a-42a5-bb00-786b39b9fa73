"""
Constantes utilizadas en el paquete novel_downloader.
"""

# HTML Parser
HTML_PARSER = 'html.parser'

# Regex patterns for volume detection
VOLUME_DECIMAL_REGEX = r'volumen-(\d+(?:\.\d+)?)'
VOLUME_HYPHEN_DECIMAL_REGEX = r'volumen-(\d+)-(\d+)'
VOLUME_GENERAL_DECIMAL_REGEX = r'volumen.*(\d+).*(\d+)'

# URL prefixes
TAB_URL_PREFIX = '#tab-'

# HTTP Headers - User Agents actualizados y realistas
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.2 Safari/605.1.15'
]

# User Agent por defecto (el más común)
USER_AGENT = USER_AGENTS[0]

# Headers más completos y naturales
ACCEPT_HEADER = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
REFERER_HEADER = 'https://novelasligeras.net/'

# Headers adicionales para parecer más humano
COMMON_HEADERS = {
    'Accept-Language': 'es-ES,es;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
    'Accept-Encoding': 'gzip, deflate, br, zstd',
    'Cache-Control': 'max-age=0',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Sec-Fetch-User': '?1',
    'Upgrade-Insecure-Requests': '1',
    'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"'
}

# File structure
CACHE_DIR = 'cache'
IMAGES_DIR = 'downloaded_images'

# Image formats
SUPPORTED_IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
DEFAULT_IMAGE_EXTENSION = '.jpg'

# Content types
CONTENT_TYPES = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp',
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'application/javascript'
}
