#!/usr/bin/env python3
"""
Pruebas avanzadas con cloudscraper usando configuraciones del repositorio VeNoMouS
https://github.com/VeNoMouS/cloudscraper
"""

import time
import random
import json
import cloudscraper
from typing import Optional, Dict, Any

# URL de prueba
TEST_URL = "https://novelasligeras.net/index.php/producto/lord-of-mysteries-novela-ligera/"

def get_advanced_headers() -> Dict[str, str]:
    """Headers más avanzados y realistas"""
    return {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'es-ES,es;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Cache-Control': 'max-age=0',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'DNT': '1',
        'Connection': 'keep-alive'
    }

def test_basic_cloudscraper(url: str) -> Optional[str]:
    """Prueba básica con cloudscraper"""
    print("🧪 Probando CloudScraper básico...")
    try:
        scraper = cloudscraper.create_scraper()
        response = scraper.get(url, timeout=30)
        
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Éxito con CloudScraper básico!")
            return response.text
        else:
            print(f"   ❌ Error HTTP: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    return None

def test_cloudscraper_with_browser_config(url: str) -> Optional[str]:
    """Prueba con configuración específica de navegador"""
    print("🧪 Probando CloudScraper con configuración de navegador...")
    
    browser_configs = [
        {'browser': 'chrome', 'platform': 'windows', 'desktop': True},
        {'browser': 'chrome', 'platform': 'darwin', 'desktop': True},
        {'browser': 'firefox', 'platform': 'windows', 'desktop': True},
        {'browser': 'chrome', 'platform': 'linux', 'desktop': True}
    ]
    
    for i, config in enumerate(browser_configs):
        try:
            print(f"   Configuración {i+1}: {config}")
            scraper = cloudscraper.create_scraper(browser=config)
            headers = get_advanced_headers()
            
            response = scraper.get(url, headers=headers, timeout=30)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ Éxito con configuración {i+1}!")
                return response.text
            else:
                print(f"   ❌ Error HTTP: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error con configuración {i+1}: {e}")
        
        time.sleep(2)  # Pausa entre intentos
    
    return None

def test_cloudscraper_with_captcha_solver(url: str) -> Optional[str]:
    """Prueba con solver de captcha (si está disponible)"""
    print("🧪 Probando CloudScraper con solver de captcha...")
    
    try:
        # Intentar con diferentes solvers
        captcha_solvers = [
            None,  # Sin solver
            # Puedes agregar solvers como 2captcha, anticaptcha, etc.
        ]
        
        for solver in captcha_solvers:
            try:
                if solver:
                    scraper = cloudscraper.create_scraper(captcha={'provider': solver})
                    print(f"   Usando solver: {solver}")
                else:
                    scraper = cloudscraper.create_scraper()
                    print("   Sin solver de captcha")
                
                headers = get_advanced_headers()
                response = scraper.get(url, headers=headers, timeout=45)
                
                print(f"   Status: {response.status_code}")
                if response.status_code == 200:
                    print("   ✅ Éxito con solver!")
                    return response.text
                else:
                    print(f"   ❌ Error HTTP: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
    except Exception as e:
        print(f"   ❌ Error general: {e}")
    
    return None

def test_cloudscraper_with_delays(url: str) -> Optional[str]:
    """Prueba con delays y reintentos inteligentes"""
    print("🧪 Probando CloudScraper con delays inteligentes...")
    
    try:
        scraper = cloudscraper.create_scraper(
            browser={
                'browser': 'chrome',
                'platform': 'windows',
                'desktop': True
            },
            delay=random.uniform(5, 10)  # Delay aleatorio
        )
        
        headers = get_advanced_headers()
        
        # Múltiples intentos con delays crecientes
        for attempt in range(3):
            try:
                delay = random.uniform(3, 8) * (attempt + 1)
                print(f"   Intento {attempt + 1} con delay de {delay:.1f}s...")
                time.sleep(delay)
                
                response = scraper.get(url, headers=headers, timeout=60)
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    print("   ✅ Éxito con delays!")
                    return response.text
                elif response.status_code == 403:
                    print("   ⚠️ 403 - Continuando con siguiente intento...")
                else:
                    print(f"   ❌ Error HTTP: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ Error en intento {attempt + 1}: {e}")
        
    except Exception as e:
        print(f"   ❌ Error general: {e}")
    
    return None

def test_cloudscraper_session_persistence(url: str) -> Optional[str]:
    """Prueba con persistencia de sesión"""
    print("🧪 Probando CloudScraper con persistencia de sesión...")
    
    try:
        scraper = cloudscraper.create_scraper(
            browser={
                'browser': 'chrome',
                'platform': 'windows',
                'desktop': True
            }
        )
        
        # Primero visitar la página principal para establecer cookies
        base_url = "https://novelasligeras.net/"
        print(f"   Visitando página base: {base_url}")
        
        headers = get_advanced_headers()
        headers['Referer'] = base_url
        
        try:
            base_response = scraper.get(base_url, headers=headers, timeout=30)
            print(f"   Status página base: {base_response.status_code}")
            
            if base_response.status_code == 200:
                print("   ✅ Página base obtenida, estableciendo sesión...")
                time.sleep(random.uniform(2, 5))
                
                # Ahora intentar la página objetivo
                print(f"   Visitando página objetivo...")
                headers['Referer'] = base_url
                
                response = scraper.get(url, headers=headers, timeout=45)
                print(f"   Status página objetivo: {response.status_code}")
                
                if response.status_code == 200:
                    print("   ✅ Éxito con persistencia de sesión!")
                    return response.text
                else:
                    print(f"   ❌ Error HTTP: {response.status_code}")
            
        except Exception as e:
            print(f"   ❌ Error en sesión: {e}")
        
    except Exception as e:
        print(f"   ❌ Error general: {e}")
    
    return None

def test_cloudscraper_with_proxies(url: str) -> Optional[str]:
    """Prueba con proxies (si están disponibles)"""
    print("🧪 Probando CloudScraper con proxies...")
    
    # Lista de proxies gratuitos (estos pueden no funcionar, solo para demostración)
    # En producción, usarías proxies de pago confiables
    proxies_list = [
        # Agregar proxies aquí si los tienes
        # {'http': 'http://proxy:port', 'https': 'https://proxy:port'}
    ]
    
    if not proxies_list:
        print("   ⚠️ No hay proxies configurados, saltando prueba...")
        return None
    
    try:
        scraper = cloudscraper.create_scraper()
        headers = get_advanced_headers()
        
        for i, proxy in enumerate(proxies_list):
            try:
                print(f"   Probando proxy {i+1}: {proxy}")
                response = scraper.get(url, headers=headers, proxies=proxy, timeout=30)
                
                print(f"   Status: {response.status_code}")
                if response.status_code == 200:
                    print(f"   ✅ Éxito con proxy {i+1}!")
                    return response.text
                    
            except Exception as e:
                print(f"   ❌ Error con proxy {i+1}: {e}")
        
    except Exception as e:
        print(f"   ❌ Error general: {e}")
    
    return None

def analyze_content(html_content: str) -> Dict[str, Any]:
    """Analiza el contenido HTML obtenido"""
    if not html_content:
        return {"success": False, "reason": "No content"}
    
    analysis = {
        "success": True,
        "length": len(html_content),
        "contains_title": "lord of mysteries" in html_content.lower(),
        "contains_product": "producto" in html_content.lower(),
        "contains_woocommerce": "woocommerce" in html_content.lower(),
        "contains_cloudflare": "cloudflare" in html_content.lower(),
        "contains_403": "403" in html_content,
        "contains_forbidden": "forbidden" in html_content.lower(),
        "contains_challenge": "challenge" in html_content.lower(),
        "contains_javascript": "<script" in html_content.lower()
    }
    
    return analysis

def main():
    """Función principal para probar configuraciones avanzadas de cloudscraper"""
    print("🚀 Iniciando pruebas avanzadas de CloudScraper")
    print(f"🎯 URL objetivo: {TEST_URL}")
    print(f"📦 Versión CloudScraper: {cloudscraper.__version__}")
    print("=" * 80)
    
    strategies = [
        ("CloudScraper Básico", test_basic_cloudscraper),
        ("CloudScraper con Configuración de Navegador", test_cloudscraper_with_browser_config),
        ("CloudScraper con Solver de Captcha", test_cloudscraper_with_captcha_solver),
        ("CloudScraper con Delays Inteligentes", test_cloudscraper_with_delays),
        ("CloudScraper con Persistencia de Sesión", test_cloudscraper_session_persistence),
        ("CloudScraper con Proxies", test_cloudscraper_with_proxies)
    ]
    
    results = {}
    successful_strategies = []
    
    for strategy_name, strategy_func in strategies:
        print(f"\n📋 Estrategia: {strategy_name}")
        print("-" * 50)
        
        try:
            html_content = strategy_func(TEST_URL)
            analysis = analyze_content(html_content)
            results[strategy_name] = analysis
            
            if analysis.get("success"):
                print(f"   📊 Longitud: {analysis['length']} caracteres")
                print(f"   🎯 Contiene título: {analysis['contains_title']}")
                print(f"   🛒 Contiene producto: {analysis['contains_product']}")
                print(f"   ⚡ WooCommerce: {analysis['contains_woocommerce']}")
                print(f"   🛡️ Cloudflare: {analysis['contains_cloudflare']}")
                print(f"   🚫 Error 403: {analysis['contains_403']}")
                print(f"   🧩 Challenge: {analysis['contains_challenge']}")
                
                if analysis['contains_title'] and analysis['contains_product']:
                    print(f"   🎉 ¡ESTRATEGIA EXITOSA! {strategy_name}")
                    successful_strategies.append(strategy_name)
                    
                    # Guardar muestra del HTML exitoso
                    filename = f"sample_{strategy_name.lower().replace(' ', '_').replace('cloudscraper_', 'cs_')}.html"
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(html_content[:10000])  # Primeros 10000 caracteres
                    print(f"   💾 Muestra guardada en {filename}")
            
        except Exception as e:
            print(f"   💥 Error crítico: {e}")
            results[strategy_name] = {"success": False, "error": str(e)}
        
        # Pausa entre estrategias
        time.sleep(random.uniform(3, 6))
    
    # Resumen final
    print("\n" + "=" * 80)
    print("📈 RESUMEN DE RESULTADOS CLOUDSCRAPER")
    print("=" * 80)
    
    for strategy, result in results.items():
        if result.get("success") and result.get("contains_title"):
            status = "✅ ÉXITO COMPLETO"
        elif result.get("success"):
            status = "⚠️ PARCIAL"
        else:
            status = "❌ FALLÓ"
        
        print(f"{strategy:40} | {status}")
    
    if successful_strategies:
        print(f"\n🎯 Estrategias completamente exitosas: {len(successful_strategies)}")
        for strategy in successful_strategies:
            print(f"   ✅ {strategy}")
        print("\n💡 Recomendación: Implementar la primera estrategia exitosa en el código principal")
    else:
        print("\n😞 Ninguna estrategia fue completamente exitosa")
        print("💡 Próximos pasos:")
        print("   - Probar con proxies de pago")
        print("   - Implementar solver de captcha")
        print("   - Usar Playwright o Selenium como alternativa")
    
    # Guardar resultados
    with open("cloudscraper_advanced_results.json", 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    print(f"\n💾 Resultados guardados en cloudscraper_advanced_results.json")

if __name__ == "__main__":
    main()
