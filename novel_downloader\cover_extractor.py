"""
Funciones para extraer y guardar portadas de novelas.
"""

import os
import hashlib
import cloudscraper
from bs4 import BeautifulSoup
from typing import List, Optional, Dict, Any
from urllib.parse import urljoin

from novel_downloader.constants import HTML_PARSER, CACHE_DIR, USER_AGENT, ACCEPT_HEADER, REFERER_HEADER


def extract_cover_images(html_content: str, base_url: str) -> List[str]:
    """
    Extrae las URLs de las imágenes de portada de la página principal.

    Args:
        html_content: El contenido HTML de la página principal
        base_url: La URL base para resolver URLs relativas

    Returns:
        Lista de URLs de imágenes de portada ordenadas por volumen
    """
    soup = BeautifulSoup(html_content, HTML_PARSER)

    # Buscar el div con la clase "woocommerce-product-gallery__wrapper"
    gallery_div = soup.find('div', class_='woocommerce-product-gallery__wrapper')

    if not gallery_div:
        print("No se encontró la galería de imágenes de portada")
        return []

    cover_urls = []

    # Buscar todas las imágenes dentro de los enlaces en la galería
    for div in gallery_div.find_all('div'):
        a_tag = div.find('a')
        if a_tag:
            img_tag = a_tag.find('img')
            if img_tag and img_tag.get('src'):
                img_url = img_tag['src']

                # Resolver URL relativa si es necesario
                if not img_url.startswith(('http://', 'https://')):
                    img_url = urljoin(base_url, img_url)

                cover_urls.append(img_url)

    return cover_urls


def download_cover_image(img_url: str, novel_title: str, volume_number: str, scraper_instance=None) -> Optional[str]:
    """
    Descarga y guarda una imagen de portada en caché.

    Args:
        img_url: La URL de la imagen de portada
        novel_title: El título de la novela
        volume_number: El número de volumen

    Returns:
        La ruta local de la imagen descargada o None si hubo un error
    """
    if not img_url:
        return None

    # Crear un nombre de archivo seguro para el título de la novela
    safe_title = "_".join(c if c.isalnum() or c in "._-" else "_" for c in novel_title.split())

    # Crear un hash de la URL para el nombre de archivo
    url_hash = hashlib.md5(img_url.encode()).hexdigest()

    # Obtener la extensión del archivo de la URL
    extension = os.path.splitext(img_url)[1]
    if not extension or len(extension) > 5:  # Si no hay extensión o es demasiado larga, usar .jpg
        extension = '.jpg'

    # Crear el nombre de archivo
    filename = f"cover_vol_{volume_number}_{url_hash}{extension}"

    # Crear el directorio de portadas si no existe
    covers_dir = os.path.join(CACHE_DIR, safe_title, "covers")
    if not os.path.exists(covers_dir):
        os.makedirs(covers_dir)

    # Ruta completa al archivo
    file_path = os.path.join(covers_dir, filename)

    # Verificar si la imagen ya está en caché
    if not os.path.exists(file_path):
        try:
            # Crear una instancia de cloudscraper si no se proporciona una
            if scraper_instance is None:
                scraper_instance = cloudscraper.create_scraper(
                    browser={
                        'browser': 'chrome',
                        'platform': 'windows',
                        'desktop': True
                    }
                )

            # Configurar headers para la solicitud
            headers = {
                'User-Agent': USER_AGENT,
                'Accept': ACCEPT_HEADER,
                'Accept-Language': 'en-US,en;q=0.5',
                'Referer': REFERER_HEADER,
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }

            # Descargar la imagen
            response = scraper_instance.get(img_url, headers=headers, stream=True)
            if response.status_code == 200:
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(1024):
                        f.write(chunk)
                print(f"Portada descargada: {img_url} -> {file_path}")
            else:
                print(f"Error al descargar portada {img_url}: HTTP {response.status_code}")
                return None
        except Exception as e:
            print(f"Error al descargar portada {img_url}: {e}")
            return None
    else:
        print(f"Usando portada en caché: {file_path}")

    return file_path


def add_covers_to_structure(structure: Dict[str, Any], cover_urls: List[str], novel_title: str, scraper_instance=None) -> Dict[str, Any]:
    """
    Añade las URLs de las portadas a la estructura de la novela.

    Args:
        structure: La estructura de la novela
        cover_urls: Lista de URLs de imágenes de portada
        novel_title: El título de la novela

    Returns:
        La estructura actualizada con las URLs de las portadas
    """
    # Crear una copia de la estructura para no modificar la original
    updated_structure = structure.copy()

    # Asegurarse de que la estructura tiene volúmenes
    if "volumes" not in updated_structure:
        return updated_structure

    # Iterar sobre los volúmenes y asignar las portadas correspondientes
    for i, volume in enumerate(updated_structure["volumes"]):
        # Extraer el número de volumen del título
        volume_title = volume.get("title", "")
        volume_number = ""

        # Intentar extraer el número de volumen (puede ser decimal como "4.5")
        if "Volumen " in volume_title:
            volume_number = volume_title.replace("Volumen ", "")

        # Asignar la URL de la portada si está disponible
        cover_url = ""
        if i < len(cover_urls):
            cover_url = cover_urls[i]

            # Descargar y guardar la portada
            if volume_number:
                cover_path = download_cover_image(cover_url, novel_title, volume_number, scraper_instance)
                if cover_path:
                    # Guardar tanto la URL como la ruta local
                    volume["cover_url"] = cover_url
                    volume["cover_path"] = cover_path
                else:
                    volume["cover_url"] = ""
                    volume["cover_path"] = ""
            else:
                volume["cover_url"] = cover_url
                volume["cover_path"] = ""
        else:
            volume["cover_url"] = ""
            volume["cover_path"] = ""

    return updated_structure
