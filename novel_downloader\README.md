# Novel Downloader

Este paquete proporciona funcionalidades para descargar novelas ligeras y convertirlas a formato EPUB.

## Estructura del Paquete

El paquete está organizado en los siguientes módulos:

- `__init__.py`: Inicialización del paquete
- `__main__.py`: Punto de entrada principal
- `constants.py`: Constantes utilizadas en el paquete
- `fetcher.py`: Funciones para obtener contenido HTML
- `structure.py`: Funciones para extraer la estructura de la novela
- `downloader.py`: Funciones para descargar contenido e imágenes
- `epub_builder.py`: Funciones para crear archivos EPUB
- `utils.py`: Funciones de utilidad
- `cli.py`: Interfaz de línea de comandos

## Uso

### Instalación

```bash
# Clonar el repositorio
git clone <url-del-repositorio>
cd <directorio-del-repositorio>

# Instalar dependencias
pip install -r requirements.txt
```

### Ejecución

```bash
# Ejecutar con la URL predeterminada
python -m novel_downloader

# Ejecutar con una URL personalizada
python -m novel_downloader --url <url-de-la-novela>

# Usar un archivo HTML local
python -m novel_downloader --file <ruta-al-archivo-html>

# Especificar archivo de salida JSON
python -m novel_downloader --output <ruta-de-salida-json>
```

## Funcionalidades

- Extracción de la estructura de novelas ligeras desde páginas web
- Detección automática de volúmenes, capítulos y secciones especiales
- Descarga de contenido e imágenes con almacenamiento en caché
- Creación de archivos EPUB con formato adecuado
- Soporte para volúmenes decimales (4.5, 7.5, etc.)
- Interfaz de línea de comandos fácil de usar

## Requisitos

- Python 3.6+
- cloudscraper
- BeautifulSoup4
- EbookLib

## Licencia

[MIT](LICENSE)
