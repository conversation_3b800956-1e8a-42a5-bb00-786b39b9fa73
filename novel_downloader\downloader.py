"""
Funciones para descargar contenido de novelas.
"""

import os
import re
import time
import random
import cloudscraper
from typing import Dict, Optional, Any, Tuple, List

from novel_downloader.constants import (USER_AGENT, ACCEPT_HEADER, REFERER_HEADER, CACHE_DIR)
from novel_downloader.content_extractor import extract_content, get_safe_filename


# get_safe_filename moved to content_extractor.py


def download_content(scraper_instance, url: str, cache_dir: str, part_title: Optional[str] = None, volume_num: Optional[str] = None) -> Optional[str]:
    """
    Descarga el contenido de una URL.

    Args:
        scraper_instance: La instancia de cloudscraper a usar
        url: La URL para descargar contenido
        cache_dir: El directorio para almacenar en caché el contenido
        part_title: El título de la parte (para mejor nomenclatura de caché)
        volume_num: El número de volumen (para mejor nomenclatura de caché)

    Returns:
        El contenido HTML
    """
    # Identificador para esta URL (comentado para referencia futura)
    # cache_key = f"{url}_{part_title}_{volume_num}"

    # Crear estructura de directorios para el volumen si no existe
    if not os.path.exists(cache_dir):
        os.makedirs(cache_dir)

    # Determinar el tipo de sección (Capítulo, Prólogo, Epílogo, Historia_Corta)
    section_type = "Capitulo"
    if part_title:
        if "prólogo" in part_title.lower():
            section_type = "Prologo"
        elif "epílogo" in part_title.lower():
            section_type = "Epilogo"
        elif "historia corta" in part_title.lower() or "ss" in part_title.lower():
            section_type = "Historia_Corta"

    # Crear un nombre de archivo seguro a partir del título de la parte
    safe_title = get_safe_filename(part_title) if part_title else ""

    # Extraer el número de parte si está disponible
    part_number = None
    if part_title and "Parte" in part_title:
        try:
            part_number_match = re.search(r'Parte\s+(\d+)', part_title)
            if part_number_match:
                part_number = part_number_match.group(1)
        except Exception as e:
            print(f"Error al extraer número de parte: {e}")

    # Crear la ruta al directorio de la parte
    if volume_num and part_title:
        section_dir = os.path.join(cache_dir, section_type)
        if not os.path.exists(section_dir):
            os.makedirs(section_dir)

        # Crear un nombre para el directorio de la parte
        part_number_str = f"{part_number}_" if part_number else ""
        part_dir_name = f"{part_number_str}{safe_title}"
        part_dir = os.path.join(section_dir, part_dir_name)
        if not os.path.exists(part_dir):
            os.makedirs(part_dir)

        # Verificar si ya tenemos el contenido HTML original en caché
        original_html_file = os.path.join(part_dir, "original.html")
        if os.path.exists(original_html_file):
            print(f"Usando contenido en caché para {url}")
            # Leer el HTML original para procesarlo nuevamente
            with open(original_html_file, 'r', encoding='utf-8') as f:
                return f.read()

    # Si no está en caché, descargarlo
    headers = {
        'User-Agent': USER_AGENT,
        'Accept': ACCEPT_HEADER,
        'Accept-Language': 'en-US,en;q=0.5',
        'Referer': REFERER_HEADER,
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    }

    for attempt in range(3):  # Intentar hasta 3 veces
        try:
            # Agregar un retraso aleatorio entre solicitudes para evitar ser bloqueado
            if attempt > 0:
                delay = random.uniform(1, 3)
                print(f"Intento de reintento {attempt+1}/3. Esperando {delay:.2f} segundos...")
                time.sleep(delay)

            print(f"Obteniendo contenido de {url}...")
            response = scraper_instance.get(url, headers=headers)

            if response.status_code == 200:
                print("Contenido obtenido con éxito!")
                html_content = response.text

                # Si tenemos un directorio de parte, guardar el HTML original
                if volume_num and part_title and os.path.exists(part_dir):
                    original_html_file = os.path.join(part_dir, "original.html")
                    with open(original_html_file, 'w', encoding='utf-8') as f:
                        f.write(html_content)

                return html_content
            else:
                print(f"Error HTTP: {response.status_code}")

        except Exception as e:
            print(f"Error al obtener contenido: {e}")

    print(f"No se pudo descargar contenido de {url} después de 3 intentos.")
    return None


# extract_content moved to content_extractor.py


def download_volumes(structure: Dict[str, Any], selected_volumes: list, novel_title: str) -> Dict[str, Tuple[str, List[Tuple[str, str, str]]]]:
    """
    Descarga el contenido de los volúmenes seleccionados.

    Args:
        structure: La estructura de la novela
        selected_volumes: Lista de números de volúmenes seleccionados
        novel_title: El título de la novela

    Returns:
        Diccionario de contenido en caché donde las claves son URLs y los valores son el contenido HTML
    """
    # Crear instancia de scraper para reutilizar
    scraper = cloudscraper.create_scraper(
        browser={
            'browser': 'chrome',
            'platform': 'windows',
            'desktop': True
        }
    )

    # Crear directorio de caché y estructura de directorios para contenido descargado
    safe_title = get_safe_filename(novel_title)

    # Estructura de directorios:
    # /cache/[novel_title]/Volume_[volume_num]/[section_type]/[part_number]_[part_title]
    cache_dir = os.path.join(CACHE_DIR, safe_title)

    # /cache/[novel_title]/Volume_[volume_num]/[section_type]/[part_number]_[part_title]/images
    # Las imágenes ahora se guardan dentro de la estructura de cache

    # Crear directorio base de cache
    if not os.path.exists(cache_dir):
        os.makedirs(cache_dir)

    content_cache = {}

    for volume_id in selected_volumes:
        # Encontrar el volumen en la estructura
        volume = next((v for v in structure["volumes"] if v["title"] == f"Volumen {volume_id}"), None)
        if not volume:
            print(f"Volumen {volume_id} no encontrado en la estructura.")
            continue

        print(f"\nDescargando contenido para {volume['title']}...")

        # Crear directorios específicos de volumen
        volume_cache_dir = os.path.join(cache_dir, f"Volume_{volume_id}")
        if not os.path.exists(volume_cache_dir):
            os.makedirs(volume_cache_dir)

        # Procesar cada sección en el volumen
        for section in volume["content"]:
            section_title = section["title"]
            print(f"  Procesando {section_title}...")

            # Procesar cada parte en la sección
            for part in section["parts"]:
                part_title = part["title"]
                part_url = part["url"]

                print(f"    Descargando {part_title}...")

                # Extraer el número de parte si está disponible
                part_number = None
                if "Parte" in part_title and "" in part_title:
                    try:
                        part_number_match = re.search(r'Parte\s+(\d+)', part_title)
                        if part_number_match:
                            part_number = part_number_match.group(1)
                    except Exception as e:
                        print(f"Error al extraer número de parte: {e}")

                # Descargar contenido con almacenamiento en caché
                html_content = download_content(
                    scraper,
                    part_url,
                    volume_cache_dir,
                    part_title,
                    volume_id
                )

                if html_content:
                    # Extraer contenido
                    content_result = extract_content(
                        html_content,
                        part_url,
                        part_title,
                        part_number,
                        volume_cache_dir,
                        scraper
                    )

                    # Almacenar en caché el contenido y las imágenes descargadas
                    if content_result and content_result[0]:  # Verificar que el contenido HTML no sea None
                        content_cache[part_url] = content_result

                # Ser amable con el servidor
                time.sleep(random.uniform(1, 2))

    return content_cache
