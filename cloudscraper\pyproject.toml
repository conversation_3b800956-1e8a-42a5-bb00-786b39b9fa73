[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "cloudscraper"
version = "3.0.0"
description = "Enhanced Python module to bypass Cloudflare's anti-bot page with support for v2 challenges, proxy rotation, and stealth mode."
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "VeNoMouS", email = "<EMAIL>"},
    {name = "<PERSON><PERSON> Bo<PERSON>", email = "<EMAIL>"}
]
maintainers = [
    {name = "<PERSON><PERSON> Bo<PERSON>", email = "<EMAIL>"},
    {name = "<PERSON><PERSON>", email = "<EMAIL>"}
]
keywords = [
    "cloudflare",
    "scraping",
    "ddos",
    "scrape",
    "webscraper",
    "anti-bot",
    "waf",
    "iuam",
    "bypass",
    "challenge"
]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Natural Language :: English",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Internet :: WWW/HTTP",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
    "Topic :: Software Development :: Libraries :: Python Modules"
]
requires-python = ">=3.8"
dependencies = [
    "requests>=2.31.0",
    "requests-toolbelt>=1.0.0",
    "pyparsing>=3.1.0",
    "pyOpenSSL>=24.0.0",
    "pycryptodome>=3.20.0",
    "websocket-client>=1.7.0",
    "js2py>=0.74",
    "brotli>=1.1.0",
    "certifi>=2024.2.2"
]

[project.optional-dependencies]
dev = [
    "pytest>=8.0.0",
    "pytest-cov>=4.0.0",
    "pytest-xdist>=3.0.0",
    "pytest-asyncio>=0.23.0",
    "black>=24.0.0",
    "isort>=5.13.0",
    "flake8>=7.0.0",
    "mypy>=1.8.0",
    "pre-commit>=3.6.0"
]
test = [
    "pytest>=8.0.0",
    "pytest-cov>=4.0.0",
    "pytest-xdist>=3.0.0",
    "pytest-asyncio>=0.23.0",
    "responses>=0.24.0"
]

[project.urls]
Homepage = "https://github.com/venomous/cloudscraper"
Repository = "https://github.com/venomous/cloudscraper.git"
Issues = "https://github.com/venomous/cloudscraper/issues"
Changelog = "https://github.com/venomous/cloudscraper/blob/master/CHANGELOG.md"

[tool.setuptools.packages.find]
include = ["cloudscraper*"]

[tool.setuptools.package-data]
cloudscraper = ["user_agent/browsers.json"]

[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["cloudscraper"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "8.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests"
]

[tool.coverage.run]
source = ["cloudscraper"]
omit = [
    "tests/*",
    "cloudscraper/interpreters/jsfuck.py"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:"
]
