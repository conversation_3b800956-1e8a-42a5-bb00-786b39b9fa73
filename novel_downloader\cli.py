"""
Funciones de interfaz de línea de comandos para el paquete novel_downloader.
"""

import argparse
import json
import os
from typing import Dict, Any, List, Optional

import cloudscraper
from novel_downloader.fetcher import fetch_html_content, load_html_from_file
from novel_downloader.structure import extract_novel_structure
from novel_downloader.utils import print_summary, select_volumes
from novel_downloader.downloader import download_volumes
from novel_downloader.epub_builder import create_epub


def parse_arguments() -> argparse.Namespace:
    """
    Analiza los argumentos de línea de comandos.

    Returns:
        Objeto Namespace con los argumentos analizados
    """
    parser = argparse.ArgumentParser(description='Descargar contenido de novela y crear EPUB.')
    parser.add_argument('--url', default="https://novelasligeras.net/index.php/producto/youkoso-jitsuryoku-shijou-shugi-no-kyoushitsu-e-novela-ligera/",
                        help='URL de la página de novela para extraer')
    parser.add_argument('--file', help='Ruta a un archivo HTML local para analizar en lugar de obtener de la web')
    parser.add_argument('--volumes', help='Lista de volúmenes a descargar (separados por comas, ej: 1,2,3)')

    return parser.parse_args()


def run_cli() -> None:
    """
    Ejecuta la interfaz de línea de comandos.
    """
    args = parse_arguments()

    # Crear instancia de cloudscraper para reutilizar
    scraper = cloudscraper.create_scraper(
        browser={
            'browser': 'chrome',
            'platform': 'windows',
            'desktop': True
        }
    )

    # Obtener contenido HTML ya sea de URL o archivo
    html_content = None
    if args.file:
        html_content = load_html_from_file(args.file)
    else:
        html_content = fetch_html_content(args.url, scraper)

    if not html_content:
        print("No se pudo obtener contenido HTML. Saliendo.")
        return

    # Extraer estructura de novela
    print("Extrayendo estructura de novela...")
    final_output, result = extract_novel_structure(html_content, args.url, scraper)

    # Obtener el título de la novela de la estructura
    novel_title = final_output["metadata"]["title"]

    # Crear directorio de caché para la novela si no existe
    safe_title = novel_title.replace(' ', '_')
    novel_cache_dir = os.path.join("cache", safe_title)
    if not os.path.exists(novel_cache_dir):
        os.makedirs(novel_cache_dir)

    # Guardar en archivo JSON dentro del directorio de caché de la novela
    json_file = os.path.join(novel_cache_dir, "novel_structure.json")
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(final_output, f, ensure_ascii=False, indent=2)

    print(f"Estructura completa de novela generada con marca de tiempo: {final_output['metadata']['generated_at']}")
    print(f"Estructura guardada en: {json_file}")

    # Imprimir resumen
    print_summary(result)

    # Obtener título de novela
    novel_title = final_output["metadata"]["title"]
    print(f"\nTítulo de novela: {novel_title}")

    # Verificar si se proporcionaron volúmenes como argumento
    if args.volumes:
        # Usar los volúmenes proporcionados como argumento
        try:
            selected_volumes = args.volumes.split(',')
            # Verificar que los volúmenes existan en la estructura
            valid_volumes = [vol for vol in selected_volumes if any(v["title"] == f"Volumen {vol}" for v in final_output["volumes"])]
            if not valid_volumes:
                print("Ninguno de los volúmenes proporcionados existe en la estructura. Seleccionando manualmente...")
                selected_volumes = select_volumes(final_output)
            else:
                selected_volumes = valid_volumes
        except Exception as e:
            print(f"Error al procesar los volúmenes proporcionados: {e}. Seleccionando manualmente...")
            selected_volumes = select_volumes(final_output)
    else:
        # Dejar que el usuario seleccione volúmenes
        selected_volumes = select_volumes(final_output)

    if not selected_volumes:
        print("No se seleccionaron volúmenes. Saliendo.")
        return

    print(f"Volúmenes seleccionados: {', '.join(selected_volumes)}")

    # Descargar volúmenes seleccionados
    content_cache = download_volumes(final_output, selected_volumes, novel_title)

    # Crear un EPUB separado para cada volumen seleccionado
    epub_filenames = []
    for volume_num in selected_volumes:
        # Formatear el número de volumen (mantener decimales si existen)
        vol_num = float(volume_num)
        vol_str = str(int(vol_num)) if vol_num.is_integer() else str(vol_num).replace('.', '-')
        epub_filename = f"{novel_title.replace(' ', '_')}_Vol_{vol_str}.epub"

        # Crear EPUB con el contenido descargado solo para este volumen
        print(f"\nCreando EPUB para Volumen {volume_num}...")
        create_epub(novel_title, [volume_num], content_cache, final_output, epub_filename)
        epub_filenames.append(epub_filename)

    # No eliminamos el archivo JSON, lo mantenemos para referencia futura

    # Mostrar mensaje de éxito con todos los archivos EPUB creados
    if len(epub_filenames) == 1:
        print(f"\n¡Todo listo! Tu EPUB ha sido creado: {epub_filenames[0]}")
    else:
        print("\n¡Todo listo! Tus EPUBs han sido creados:")
        for filename in epub_filenames:
            print(f"  - {filename}")
