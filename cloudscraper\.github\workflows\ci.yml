name: CI

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  release:
    types: [ published ]

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12", "3.13"]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .[dev,test]

    - name: Lint with flake8
      run: |
        flake8 cloudscraper --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 cloudscraper --count --exit-zero --max-complexity=10 --max-line-length=88 --statistics

    - name: Format check with black
      run: |
        black --check cloudscraper

    - name: Import sort check with isort
      run: |
        isort --check-only cloudscraper

    - name: Type check with mypy
      run: |
        mypy cloudscraper
      continue-on-error: true

    - name: Test with pytest
      run: |
        pytest tests/ -v --cov=cloudscraper --cov-report=xml --cov-report=term-missing

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3.11"

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install bandit[toml] safety

    - name: Run bandit security linter
      run: |
        bandit -r cloudscraper -f json -o bandit-report.json
      continue-on-error: true

    - name: Run safety check
      run: |
        safety check --json --output safety-report.json
      continue-on-error: true

  build:
    needs: [test]
    runs-on: ubuntu-latest
    if: github.event_name == 'release'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3.11"

    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine

    - name: Build package
      run: |
        python -m build

    - name: Check package
      run: |
        twine check dist/*

    - name: Publish to PyPI
      if: github.event_name == 'release'
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
      run: |
        twine upload dist/*
