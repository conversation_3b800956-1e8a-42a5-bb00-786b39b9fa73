"""
Funciones para obtener contenido HTML de URLs o archivos locales.
"""

import time
import random
import cloudscraper
from typing import Optional

from novel_downloader.constants import (
    USER_AGENT, USER_AGENTS, ACCEPT_HEADER, REFERER_HEADER, COMMON_HEADERS
)

# Importaciones opcionales para métodos alternativos
try:
    import undetected_chromedriver as uc
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

try:
    import curl_cffi.requests as curl_requests
    CURL_CFFI_AVAILABLE = True
except ImportError:
    CURL_CFFI_AVAILABLE = False

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

try:
    from playwright.sync_api import sync_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False


def get_random_headers() -> dict:
    """
    Genera headers HTTP aleatorios y realistas.

    Returns:
        Diccionario con headers HTTP
    """
    user_agent = random.choice(USER_AGENTS)
    headers = COMMON_HEADERS.copy()
    headers.update({
        'User-Agent': user_agent,
        'Accept': ACCEPT_HEADER,
        'Referer': REFERER_HEADER,
        'DNT': '1',
        'Connection': 'keep-alive'
    })
    return headers


def fetch_with_selenium(url: str, max_retries: int = 3) -> Optional[str]:
    """
    Obtiene contenido HTML usando Selenium con undetected-chromedriver.

    Args:
        url: La URL para obtener el contenido
        max_retries: Número máximo de intentos de reintento

    Returns:
        Contenido HTML como string si tiene éxito, None en caso contrario
    """
    if not SELENIUM_AVAILABLE:
        return None

    for attempt in range(max_retries):
        driver = None
        try:
            print(f"Intentando con Selenium (intento {attempt + 1}/{max_retries})...")

            # Obtener User Agent aleatorio
            user_agent = random.choice(USER_AGENTS)
            print(f"Usando User Agent: {user_agent[:50]}...")

            # Configurar opciones de Chrome más naturales
            options = uc.ChromeOptions()

            # Configuraciones básicas
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            options.add_argument('--disable-default-apps')
            options.add_argument('--disable-background-timer-throttling')
            options.add_argument('--disable-backgrounding-occluded-windows')
            options.add_argument('--disable-renderer-backgrounding')
            options.add_argument('--disable-features=TranslateUI')
            options.add_argument('--disable-ipc-flooding-protection')

            # Configurar ventana y user agent
            options.add_argument('--window-size=1366,768')  # Resolución más común
            options.add_argument(f'--user-agent={user_agent}')

            # Configuraciones para evitar detección
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            options.add_experimental_option("detach", True)

            # Preferencias del navegador
            prefs = {
                "profile.default_content_setting_values": {
                    "notifications": 2,  # Bloquear notificaciones
                    "media_stream": 2,   # Bloquear cámara/micrófono
                },
                "profile.managed_default_content_settings": {
                    "images": 2  # Bloquear imágenes para cargar más rápido
                }
            }
            options.add_experimental_option("prefs", prefs)

            # Crear driver con manejo de errores mejorado
            try:
                driver = uc.Chrome(options=options, version_main=None)
            except Exception as chrome_error:
                print(f"Error creando driver Chrome: {chrome_error}")
                # Intentar sin opciones específicas
                try:
                    basic_options = uc.ChromeOptions()
                    basic_options.add_argument('--no-sandbox')
                    basic_options.add_argument('--disable-dev-shm-usage')
                    basic_options.add_argument(f'--user-agent={user_agent}')
                    driver = uc.Chrome(options=basic_options)
                except Exception as basic_error:
                    print(f"Error con configuración básica: {basic_error}")
                    raise chrome_error

            # Configurar timeouts
            driver.set_page_load_timeout(60)
            driver.implicitly_wait(15)

            # Scripts para ocultar automatización
            stealth_scripts = [
                "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})",
                "Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})",
                "Object.defineProperty(navigator, 'languages', {get: () => ['es-ES', 'es', 'en']})",
                "window.chrome = { runtime: {} }",
                "Object.defineProperty(navigator, 'permissions', {get: () => ({query: () => Promise.resolve({state: 'granted'})})})"
            ]

            for script in stealth_scripts:
                try:
                    driver.execute_script(script)
                except:
                    pass

            # Navegar a la URL
            print(f"Navegando a: {url}")
            driver.get(url)

            # Esperar carga inicial
            initial_wait = random.uniform(4, 8)
            print(f"Esperando {initial_wait:.1f} segundos para carga inicial...")
            time.sleep(initial_wait)

            # Simular comportamiento humano: scroll
            try:
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight/4);")
                time.sleep(random.uniform(1, 2))
                driver.execute_script("window.scrollTo(0, 0);")
                time.sleep(random.uniform(1, 2))
            except:
                pass

            # Obtener el HTML
            html_content = driver.page_source

            if html_content and len(html_content) > 1000:
                # Verificar que no sea una página de error
                error_indicators = ["403", "forbidden", "access denied", "blocked", "cloudflare"]
                if not any(indicator in html_content.lower() for indicator in error_indicators):
                    print("✅ Página obtenida con éxito usando Selenium!")
                    return html_content
                else:
                    print("❌ Página obtenida pero contiene indicadores de bloqueo")
            else:
                print("❌ Contenido HTML vacío o muy pequeño")

        except Exception as e:
            print(f"❌ Error con Selenium: {e}")

        finally:
            if driver:
                try:
                    driver.quit()
                except Exception:
                    pass

        if attempt < max_retries - 1:
            delay = random.uniform(5, 12)
            print(f"⏳ Esperando {delay:.1f} segundos antes del siguiente intento...")
            time.sleep(delay)

    return None


def fetch_with_playwright(url: str, max_retries: int = 3) -> Optional[str]:
    """
    Obtiene contenido HTML usando Playwright (más moderno y efectivo).

    Args:
        url: La URL para obtener el contenido
        max_retries: Número máximo de intentos de reintento

    Returns:
        Contenido HTML como string si tiene éxito, None en caso contrario
    """
    if not PLAYWRIGHT_AVAILABLE:
        return None

    for attempt in range(max_retries):
        try:
            print(f"Intentando con Playwright (intento {attempt + 1}/{max_retries})...")

            # Obtener User Agent aleatorio
            user_agent = random.choice(USER_AGENTS)
            print(f"Usando User Agent: {user_agent[:50]}...")

            with sync_playwright() as p:
                # Lanzar navegador con configuración stealth
                browser = p.chromium.launch(
                    headless=True,
                    args=[
                        '--no-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-blink-features=AutomationControlled',
                        '--disable-extensions',
                        '--disable-plugins',
                        '--disable-default-apps',
                        '--disable-background-timer-throttling',
                        '--disable-backgrounding-occluded-windows',
                        '--disable-renderer-backgrounding',
                        '--disable-features=TranslateUI',
                        '--disable-ipc-flooding-protection',
                        '--window-size=1366,768'
                    ]
                )

                # Crear contexto con configuración realista
                context = browser.new_context(
                    user_agent=user_agent,
                    viewport={'width': 1366, 'height': 768},
                    locale='es-ES',
                    timezone_id='Europe/Madrid',
                    permissions=['geolocation'],
                    extra_http_headers=get_random_headers()
                )

                # Crear página
                page = context.new_page()

                # Configurar timeouts
                page.set_default_timeout(60000)  # 60 segundos
                page.set_default_navigation_timeout(60000)

                # Scripts stealth para ocultar automatización
                stealth_script = """
                    Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                    Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
                    Object.defineProperty(navigator, 'languages', {get: () => ['es-ES', 'es', 'en']});
                    window.chrome = { runtime: {} };
                    Object.defineProperty(navigator, 'permissions', {
                        get: () => ({query: () => Promise.resolve({state: 'granted'})})
                    });
                """

                # Navegar a la página
                print(f"Navegando a: {url}")
                response = page.goto(url, wait_until='domcontentloaded')

                if response and response.status == 200:
                    # Ejecutar script stealth
                    page.evaluate(stealth_script)

                    # Esperar un poco para que cargue el contenido
                    wait_time = random.uniform(3, 6)
                    print(f"Esperando {wait_time:.1f} segundos para carga completa...")
                    page.wait_for_timeout(int(wait_time * 1000))

                    # Simular comportamiento humano
                    try:
                        page.evaluate("window.scrollTo(0, document.body.scrollHeight/4);")
                        page.wait_for_timeout(random.randint(1000, 2000))
                        page.evaluate("window.scrollTo(0, 0);")
                        page.wait_for_timeout(random.randint(1000, 2000))
                    except Exception:
                        pass

                    # Obtener el HTML
                    html_content = page.content()

                    # Cerrar navegador
                    browser.close()

                    if html_content and len(html_content) > 1000:
                        # Verificar que no sea una página de error
                        error_indicators = ["403", "forbidden", "access denied", "blocked", "cloudflare"]
                        if not any(indicator in html_content.lower() for indicator in error_indicators):
                            print("✅ Página obtenida con éxito usando Playwright!")
                            return html_content
                        else:
                            print("❌ Página obtenida pero contiene indicadores de bloqueo")
                    else:
                        print("❌ Contenido HTML vacío o muy pequeño")
                else:
                    print(f"❌ Error HTTP: {response.status if response else 'Sin respuesta'}")
                    browser.close()

        except Exception as e:
            print(f"❌ Error con Playwright: {e}")

        if attempt < max_retries - 1:
            delay = random.uniform(4, 8)
            print(f"⏳ Esperando {delay:.1f} segundos antes del siguiente intento...")
            time.sleep(delay)

    return None


def fetch_with_curl_cffi(url: str, max_retries: int = 3) -> Optional[str]:
    """
    Obtiene contenido HTML usando curl-cffi con headers aleatorios.

    Args:
        url: La URL para obtener el contenido
        max_retries: Número máximo de intentos de reintento

    Returns:
        Contenido HTML como string si tiene éxito, None en caso contrario
    """
    if not CURL_CFFI_AVAILABLE:
        return None

    # Versiones de Chrome para imitar
    chrome_versions = ["chrome110", "chrome116", "chrome120", "chrome124"]

    for attempt in range(max_retries):
        try:
            # Obtener headers aleatorios
            headers = get_random_headers()
            chrome_version = random.choice(chrome_versions)

            print(f"Intentando con curl-cffi (intento {attempt + 1}/{max_retries})...")
            print(f"Usando User Agent: {headers['User-Agent'][:50]}...")
            print(f"Imitando: {chrome_version}")

            # Usar curl-cffi que imita mejor el comportamiento de un navegador real
            response = curl_requests.get(
                url,
                headers=headers,
                timeout=45,
                impersonate=chrome_version,
                allow_redirects=True,
                verify=True
            )

            if response.status_code == 200:
                print("✅ Página obtenida con éxito usando curl-cffi!")
                return response.text
            else:
                print(f"❌ Error HTTP con curl-cffi: {response.status_code}")

        except Exception as e:
            print(f"❌ Error con curl-cffi: {e}")

        if attempt < max_retries - 1:
            delay = random.uniform(2, 5)
            print(f"⏳ Esperando {delay:.1f} segundos antes del siguiente intento...")
            time.sleep(delay)

    return None


def fetch_with_requests(url: str, max_retries: int = 3) -> Optional[str]:
    """
    Obtiene contenido HTML usando requests básico con headers aleatorios.

    Args:
        url: La URL para obtener el contenido
        max_retries: Número máximo de intentos de reintento

    Returns:
        Contenido HTML como string si tiene éxito, None en caso contrario
    """
    if not REQUESTS_AVAILABLE:
        return None

    for attempt in range(max_retries):
        try:
            # Obtener headers aleatorios para cada intento
            headers = get_random_headers()

            print(f"Intentando con requests mejorado (intento {attempt + 1}/{max_retries})...")
            print(f"Usando User Agent: {headers['User-Agent'][:50]}...")

            # Crear nueva sesión para cada intento
            session = requests.Session()
            session.headers.update(headers)

            # Configurar la sesión para parecer más humana
            session.max_redirects = 10

            response = session.get(
                url,
                timeout=45,
                allow_redirects=True,
                verify=True,
                stream=False
            )

            if response.status_code == 200:
                print("✅ Página obtenida con éxito usando requests!")
                return response.text
            else:
                print(f"❌ Error HTTP con requests: {response.status_code}")

        except Exception as e:
            print(f"❌ Error con requests: {e}")

        if attempt < max_retries - 1:
            delay = random.uniform(2, 4)
            print(f"⏳ Esperando {delay:.1f} segundos antes del siguiente intento...")
            time.sleep(delay)

    return None


def fetch_html_content(url: str, scraper_instance=None, max_retries: int = 3) -> Optional[str]:
    """
    Obtiene contenido HTML de una URL usando múltiples métodos.
    Intenta cloudscraper primero, luego métodos alternativos si falla.

    Args:
        url: La URL para obtener el contenido
        scraper_instance: Instancia de cloudscraper (opcional)
        max_retries: Número máximo de intentos de reintento por método

    Returns:
        Contenido HTML como string si tiene éxito, None en caso contrario
    """
    print(f"Intentando obtener contenido de: {url}")

    # Método 1: Intentar con cloudscraper primero
    print("\n=== Método 1: CloudScraper ===")
    result = _fetch_with_cloudscraper(url, scraper_instance, max_retries)
    if result:
        return result

    # Método 2: Intentar con Playwright (más moderno y efectivo)
    if PLAYWRIGHT_AVAILABLE:
        print("\n=== Método 2: Playwright (recomendado) ===")
        result = fetch_with_playwright(url, max_retries)
        if result:
            return result
    else:
        print("\n=== Playwright no disponible (instalar: pip install playwright && playwright install chromium) ===")

    # Método 3: Intentar con Selenium si está disponible
    if SELENIUM_AVAILABLE:
        print("\n=== Método 3: Selenium (undetected-chromedriver) ===")
        result = fetch_with_selenium(url, max_retries)
        if result:
            return result
    else:
        print("\n=== Selenium no disponible (instalar: pip install undetected-chromedriver) ===")

    # Método 4: Intentar con curl-cffi si está disponible
    if CURL_CFFI_AVAILABLE:
        print("\n=== Método 4: curl-cffi ===")
        result = fetch_with_curl_cffi(url, max_retries)
        if result:
            return result
    else:
        print("\n=== curl-cffi no disponible (instalar: pip install curl-cffi) ===")

    # Método 5: Intentar con requests básico como último recurso
    if REQUESTS_AVAILABLE:
        print("\n=== Método 5: Requests mejorado (último recurso) ===")
        result = fetch_with_requests(url, max_retries)
        if result:
            return result

    print("\n❌ Todos los métodos fallaron. No se pudo obtener la página.")
    print("💡 Sugerencias:")
    print("   1. Instalar dependencias adicionales (recomendado):")
    print("      pip install playwright undetected-chromedriver curl-cffi")
    print("      playwright install chromium")
    print("   2. Usar la opción --file con un archivo HTML guardado manualmente")
    print("   3. Intentar más tarde (el sitio puede estar temporalmente bloqueando bots)")
    print("   4. Verificar que Chrome esté instalado en el sistema para Selenium")

    return None


def _fetch_with_cloudscraper(url: str, scraper_instance=None, max_retries: int = 3) -> Optional[str]:
    """
    Método interno para obtener contenido con cloudscraper usando headers aleatorios.
    """
    # Configuraciones de navegador para cloudscraper
    browser_configs = [
        {'browser': 'chrome', 'platform': 'windows', 'desktop': True},
        {'browser': 'chrome', 'platform': 'darwin', 'desktop': True},
        {'browser': 'firefox', 'platform': 'windows', 'desktop': True},
    ]

    for attempt in range(max_retries):
        try:
            # Obtener headers aleatorios para cada intento
            headers = get_random_headers()

            # Usar configuración de navegador aleatoria
            browser_config = random.choice(browser_configs)

            print(f"Intentando con CloudScraper (intento {attempt + 1}/{max_retries})...")
            print(f"Usando User Agent: {headers['User-Agent'][:50]}...")
            print(f"Configuración: {browser_config['browser']} en {browser_config['platform']}")

            # Crear nueva instancia de scraper para cada intento si no se proporciona una
            if scraper_instance is None:
                scraper = cloudscraper.create_scraper(browser=browser_config)
            else:
                scraper = scraper_instance

            # Agregar un retraso aleatorio entre solicitudes
            if attempt > 0:
                delay = random.uniform(2, 5)
                print(f"⏳ Esperando {delay:.1f} segundos...")
                time.sleep(delay)

            response = scraper.get(url, headers=headers, timeout=45)

            # Verificar si obtuvimos una respuesta exitosa
            if response.status_code == 200:
                print("✅ Página obtenida con éxito con CloudScraper!")
                return response.text
            elif response.status_code == 403:
                print("❌ Acceso prohibido (403). Protección avanzada detectada.")
            else:
                print(f"❌ Error HTTP: {response.status_code}")

        except Exception as e:
            print(f"❌ Error con CloudScraper: {e}")

    print("❌ CloudScraper falló después de todos los intentos.")
    return None


def load_html_from_file(file_path: str) -> Optional[str]:
    """
    Carga contenido HTML desde un archivo local.

    Args:
        file_path: Ruta al archivo HTML

    Returns:
        Contenido HTML como string si tiene éxito, None en caso contrario
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        print(f"Contenido HTML cargado con éxito desde {file_path}")
        return html_content
    except Exception as e:
        print(f"Error al cargar el archivo: {e}")
        return None
