#!/usr/bin/env python3
"""
Playground para probar diferentes estrategias de bypass para obtener HTML
de sitios web protegidos como novelasligeras.net
"""

import time
import random
import json
from typing import Optional, Dict, Any

# URL de prueba
TEST_URL = "https://novelasligeras.net/index.php/producto/lord-of-mysteries-novela-ligera/"

# User Agents actualizados y realistas
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.2 Safari/605.1.15'
]

def get_realistic_headers(user_agent: str = None) -> Dict[str, str]:
    """Genera headers HTTP realistas"""
    if not user_agent:
        user_agent = random.choice(USER_AGENTS)
    
    return {
        'User-Agent': user_agent,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'es-ES,es;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Cache-Control': 'max-age=0',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Referer': 'https://novelasligeras.net/'
    }

def test_basic_requests(url: str) -> Optional[str]:
    """Prueba con requests básico"""
    try:
        import requests
        print("🧪 Probando con requests básico...")
        
        headers = get_realistic_headers()
        print(f"   User-Agent: {headers['User-Agent'][:50]}...")
        
        session = requests.Session()
        session.headers.update(headers)
        
        response = session.get(url, timeout=30, allow_redirects=True)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Éxito con requests básico!")
            return response.text
        else:
            print(f"   ❌ Error HTTP: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    return None

def test_cloudscraper(url: str) -> Optional[str]:
    """Prueba con cloudscraper"""
    try:
        import cloudscraper
        print("🧪 Probando con CloudScraper...")
        
        headers = get_realistic_headers()
        print(f"   User-Agent: {headers['User-Agent'][:50]}...")
        
        scraper = cloudscraper.create_scraper(
            browser={
                'browser': 'chrome',
                'platform': 'windows',
                'desktop': True
            }
        )
        
        response = scraper.get(url, headers=headers, timeout=30)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Éxito con CloudScraper!")
            return response.text
        else:
            print(f"   ❌ Error HTTP: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    return None

def test_curl_cffi(url: str) -> Optional[str]:
    """Prueba con curl-cffi"""
    try:
        import curl_cffi.requests as curl_requests
        print("🧪 Probando con curl-cffi...")
        
        headers = get_realistic_headers()
        print(f"   User-Agent: {headers['User-Agent'][:50]}...")
        
        chrome_versions = ["chrome110", "chrome116", "chrome120", "chrome124"]
        chrome_version = random.choice(chrome_versions)
        print(f"   Imitando: {chrome_version}")
        
        response = curl_requests.get(
            url,
            headers=headers,
            timeout=30,
            impersonate=chrome_version,
            allow_redirects=True
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Éxito con curl-cffi!")
            return response.text
        else:
            print(f"   ❌ Error HTTP: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    return None

def test_playwright(url: str) -> Optional[str]:
    """Prueba con Playwright"""
    try:
        from playwright.sync_api import sync_playwright
        print("🧪 Probando con Playwright...")
        
        user_agent = random.choice(USER_AGENTS)
        print(f"   User-Agent: {user_agent[:50]}...")
        
        with sync_playwright() as p:
            browser = p.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-extensions',
                    '--disable-plugins'
                ]
            )
            
            context = browser.new_context(
                user_agent=user_agent,
                viewport={'width': 1366, 'height': 768},
                locale='es-ES',
                extra_http_headers=get_realistic_headers(user_agent)
            )
            
            page = context.new_page()
            page.set_default_timeout(45000)
            
            # Script stealth
            stealth_script = """
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
                window.chrome = { runtime: {} };
            """
            
            response = page.goto(url, wait_until='domcontentloaded')
            
            if response and response.status == 200:
                page.evaluate(stealth_script)
                page.wait_for_timeout(3000)  # Esperar 3 segundos
                
                html_content = page.content()
                browser.close()
                
                if html_content and len(html_content) > 1000:
                    error_indicators = ["403", "forbidden", "access denied", "blocked"]
                    if not any(indicator in html_content.lower() for indicator in error_indicators):
                        print("   ✅ Éxito con Playwright!")
                        return html_content
                    else:
                        print("   ❌ Página bloqueada")
                else:
                    print("   ❌ Contenido vacío")
            else:
                print(f"   ❌ Error HTTP: {response.status if response else 'Sin respuesta'}")
                browser.close()
                
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    return None

def test_selenium_undetected(url: str) -> Optional[str]:
    """Prueba con Selenium undetected"""
    try:
        import undetected_chromedriver as uc
        print("🧪 Probando con Selenium (undetected-chromedriver)...")
        
        user_agent = random.choice(USER_AGENTS)
        print(f"   User-Agent: {user_agent[:50]}...")
        
        options = uc.ChromeOptions()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument(f'--user-agent={user_agent}')
        
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        driver = uc.Chrome(options=options)
        driver.set_page_load_timeout(45)
        
        # Scripts stealth
        stealth_scripts = [
            "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})",
            "window.chrome = { runtime: {} }"
        ]
        
        driver.get(url)
        
        for script in stealth_scripts:
            try:
                driver.execute_script(script)
            except Exception:
                pass
        
        time.sleep(random.uniform(3, 6))
        
        html_content = driver.page_source
        driver.quit()
        
        if html_content and len(html_content) > 1000:
            error_indicators = ["403", "forbidden", "access denied", "blocked"]
            if not any(indicator in html_content.lower() for indicator in error_indicators):
                print("   ✅ Éxito con Selenium!")
                return html_content
            else:
                print("   ❌ Página bloqueada")
        else:
            print("   ❌ Contenido vacío")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    return None

def analyze_content(html_content: str) -> Dict[str, Any]:
    """Analiza el contenido HTML obtenido"""
    if not html_content:
        return {"success": False, "reason": "No content"}
    
    analysis = {
        "success": True,
        "length": len(html_content),
        "contains_title": "lord of mysteries" in html_content.lower(),
        "contains_product": "producto" in html_content.lower(),
        "contains_woocommerce": "woocommerce" in html_content.lower(),
        "contains_cloudflare": "cloudflare" in html_content.lower(),
        "contains_403": "403" in html_content,
        "contains_forbidden": "forbidden" in html_content.lower()
    }
    
    return analysis

def main():
    """Función principal para probar todas las estrategias"""
    print("🚀 Iniciando pruebas de bypass para novelasligeras.net")
    print(f"🎯 URL objetivo: {TEST_URL}")
    print("=" * 80)
    
    strategies = [
        ("Requests básico", test_basic_requests),
        ("CloudScraper", test_cloudscraper),
        ("curl-cffi", test_curl_cffi),
        ("Playwright", test_playwright),
        ("Selenium Undetected", test_selenium_undetected)
    ]
    
    results = {}
    
    for strategy_name, strategy_func in strategies:
        print(f"\n📋 Estrategia: {strategy_name}")
        print("-" * 40)
        
        try:
            html_content = strategy_func(TEST_URL)
            analysis = analyze_content(html_content)
            results[strategy_name] = analysis
            
            if analysis.get("success"):
                print(f"   📊 Longitud: {analysis['length']} caracteres")
                print(f"   🎯 Contiene título: {analysis['contains_title']}")
                print(f"   🛒 Contiene producto: {analysis['contains_product']}")
                print(f"   ⚡ WooCommerce: {analysis['contains_woocommerce']}")
                print(f"   🛡️ Cloudflare: {analysis['contains_cloudflare']}")
                print(f"   🚫 Error 403: {analysis['contains_403']}")
                
                if analysis['contains_title'] and analysis['contains_product']:
                    print(f"   🎉 ¡ESTRATEGIA EXITOSA! {strategy_name} funcionó correctamente")
                    
                    # Guardar muestra del HTML exitoso
                    with open(f"sample_{strategy_name.lower().replace(' ', '_')}.html", 'w', encoding='utf-8') as f:
                        f.write(html_content[:5000])  # Primeros 5000 caracteres
                    print(f"   💾 Muestra guardada en sample_{strategy_name.lower().replace(' ', '_')}.html")
            
        except Exception as e:
            print(f"   💥 Error crítico: {e}")
            results[strategy_name] = {"success": False, "error": str(e)}
        
        # Pausa entre estrategias para no sobrecargar el servidor
        time.sleep(random.uniform(2, 4))
    
    # Resumen final
    print("\n" + "=" * 80)
    print("📈 RESUMEN DE RESULTADOS")
    print("=" * 80)
    
    successful_strategies = []
    for strategy, result in results.items():
        status = "✅ ÉXITO" if result.get("success") and result.get("contains_title") else "❌ FALLÓ"
        print(f"{strategy:20} | {status}")
        
        if result.get("success") and result.get("contains_title"):
            successful_strategies.append(strategy)
    
    if successful_strategies:
        print(f"\n🎯 Estrategias exitosas: {', '.join(successful_strategies)}")
        print("💡 Recomendación: Usar la primera estrategia exitosa como método principal")
    else:
        print("\n😞 Ninguna estrategia fue completamente exitosa")
        print("💡 Sugerencias:")
        print("   - Probar con proxies")
        print("   - Intentar en diferentes horarios")
        print("   - Usar descarga manual del HTML")
    
    # Guardar resultados en JSON
    with open("bypass_test_results.json", 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    print(f"\n💾 Resultados detallados guardados en bypass_test_results.json")

if __name__ == "__main__":
    main()
