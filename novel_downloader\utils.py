"""
Funciones de utilidad para el paquete novel_downloader.
"""

import os
import re
from typing import List, Dict, Any, Optional


def is_valid_decimal_part(decimal_part: str) -> bool:
    """
    Valida si una parte decimal es válida (ej: 5, 25, 75).
    
    Args:
        decimal_part: La parte decimal a validar
        
    Returns:
        True si es una parte decimal válida, False en caso contrario
    """
    # Solo aceptar partes decimales específicas que se usan comúnmente en novelas ligeras
    return decimal_part in ['5', '25', '75']


def print_summary(result: Dict[str, Any]) -> None:
    """
    Imprime un resumen de la estructura de la novela extraída.
    
    Args:
        result: El diccionario de resultado sin procesar
    """
    total_volumes = len(result)
    total_chapters = sum(len([k for k in chapters.keys() if k.isdigit()]) for chapters in result.values())
    total_parts = sum(len(parts) for chapters in result.values() for key, parts in chapters.items() if key.isdigit())

    print("\nResumen:")
    print(f"Se encontraron {total_volumes} volúmenes")
    print(f"Se encontraron {total_chapters} capítulos")
    print(f"Se encontraron {total_parts} partes de capítulos")

    # Imprimir volúmenes y sus capítulos
    print("\nVolúmenes y Capítulos:")
    for vol_num in sorted(result.keys(), key=lambda x: float(x)):
        chapter_count = len([k for k in result[vol_num].keys() if k.isdigit()])
        special_sections = []

        # Verificar secciones especiales y su contenido
        has_prologue = 'prologue' in result[vol_num] and result[vol_num]['prologue']
        has_epilogue = 'epilogue' in result[vol_num] and result[vol_num]['epilogue']
        has_short_story = 'short_story' in result[vol_num] and result[vol_num]['short_story']

        if has_prologue:
            special_sections.append("Prólogo")
        if has_epilogue:
            special_sections.append("Epílogo")
        if has_short_story:
            special_sections.append("Historias Cortas")

        # Crear una salida más descriptiva
        special_sections_str = f" + {', '.join(special_sections)}" if special_sections else ""

        # Para volúmenes sin capítulos pero con secciones especiales, proporcionar una descripción más clara
        if chapter_count == 0 and special_sections:
            print(f"Volumen {vol_num}: Sin capítulos, pero tiene {', '.join(special_sections)}")
        else:
            print(f"Volumen {vol_num}: {chapter_count} capítulos{special_sections_str}")


def select_volumes(structure: Dict[str, Any]) -> List[str]:
    """
    Permite al usuario seleccionar volúmenes para descargar.
    
    Args:
        structure: La estructura de la novela
        
    Returns:
        Lista de números de volúmenes seleccionados
    """
    print("\nVolúmenes disponibles:")
    volumes = [vol for vol in structure["volumes"]]

    for i, volume in enumerate(volumes, 1):
        print(f"{i}. {volume['title']}")

    print(f"{len(volumes) + 1}. Todos los volúmenes")

    while True:
        try:
            choice = input("\nIngrese su elección (números separados por comas o 'all'): ")

            if choice.lower() == 'all':
                return [vol["title"].split(" ")[1] for vol in volumes]

            selected = []
            for num in choice.split(','):
                num = num.strip()
                if not num:
                    continue

                index = int(num) - 1
                if index < 0 or index >= len(volumes):
                    if index == len(volumes):  # Opción "Todos los volúmenes"
                        return [vol["title"].split(" ")[1] for vol in volumes]
                    print(f"Elección inválida: {num}. Por favor, inténtelo de nuevo.")
                    selected = []
                    break

                selected.append(volumes[index]["title"].split(" ")[1])

            if selected:
                return selected

        except ValueError:
            print("Por favor, ingrese números válidos separados por comas.")
