package main

import (
	"fmt"
	"log"
	"time"

	cloudscraper "github.com/Advik-B/cloudscraper/lib"
	"github.com/Advik-B/cloudscraper/lib/js"
	useragent "github.com/Advik-B/cloudscraper/lib/user_agent"
)

func main() {
	var scraperOptions []cloudscraper.ScraperOption

	// Use an external JS runtime for better challenge compatibility
	scraperOptions = append(scraperOptions, cloudscraper.WithJSRuntime(js.Node))

	// Customize the browser to appear as Chrome on Windows
	scraperOptions = append(scraperOptions, cloudscraper.WithBrowser(useragent.Config{
		Browser:  "chrome",
		Platform: "windows",
	}))

	// Customize session handling
	scraperOptions = append(scraperOptions, cloudscraper.WithSessionConfig(
		true,           // Auto-refresh on 403s
		30*time.Minute, // Refresh session every 30 mins
		5,              // Max 403 retries
	))

	// Create the scraper with all our options
	sc, err := cloudscraper.New(scraperOptions...)
	if err != nil {
		log.Fatalf("Failed to create scraper: %v", err)
	}

	// Use the scraper...
	resp, err := sc.Get("https://novelasligeras.net/index.php/producto/lord-of-mysteries-novela-ligera/https://novelasligeras.net/index.php/producto/lord-of-mysteries-novela-ligera/")
	if err != nil {
		log.Fatal(err)
	}
	defer resp.Body.Close()

	fmt.Println("Success:", resp.Status)
}
