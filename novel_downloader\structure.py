"""
Funciones para extraer la estructura de la novela del contenido HTML.
"""

import re
import datetime
from collections import defaultdict
from typing import Dict, List, Any, Tuple
from bs4 import BeautifulSoup

from novel_downloader.constants import (
    HTML_PARSER, VOLUME_DECIMAL_REGEX, VOLUME_HYPHEN_DECIMAL_REGEX,
    VOLUME_GENERAL_DECIMAL_REGEX, TAB_URL_PREFIX
)
from novel_downloader.utils import is_valid_decimal_part
from novel_downloader.cover_extractor import extract_cover_images, add_covers_to_structure


def extract_novel_structure(html_content: str, url: str = "", scraper_instance=None) -> Tuple[Dict[str, Any], Dict[str, Any]]:
    """
    Extrae la estructura de la novela del contenido HTML.

    Args:
        html_content: Contenido HTML como string
        url: URL original para extraer el título de la novela

    Returns:
        Diccionario que contiene la estructura de la novela y el resultado sin procesar
    """
    # Obtener fecha y hora actual para la salida
    current_datetime = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Extraer título de la novela de la URL
    novel_title = "Unknown Novel"
    if url:
        # Obtener la parte después de /producto/ y antes de cualquier barra diagonal final
        url_parts = url.split('/producto/')
        if len(url_parts) > 1:
            title_part = url_parts[1].split('/')[0]
            # Convertir guiones a espacios y capitalizar
            novel_title = ' '.join(word.capitalize() for word in title_part.replace('-', ' ').split())

    # Analizar el HTML
    soup = BeautifulSoup(html_content, HTML_PARSER)

    # Extraer información del capítulo de los enlaces
    chapter_links = []
    for link in soup.find_all('a'):
        text = link.get_text(strip=True)
        href = link.get('href')

        if not (text and href):
            continue

        # Verificar si el enlace contiene información del capítulo
        chapter_match = re.search(r'capitulo-(\d+)-parte-(\d+)', href.lower())
        if chapter_match:
            chapter_num = chapter_match.group(1)
            part_num = chapter_match.group(2)

            # Extraer número de volumen de la URL
            # Primero, verificar el formato decimal explícito (ej: volumen-4.5)
            url_volume_match = re.search(VOLUME_DECIMAL_REGEX, href.lower())

            # Si no se encuentra, verificar el formato con guiones (ej: volumen-4-5)
            if not url_volume_match:
                url_volume_match = re.search(VOLUME_HYPHEN_DECIMAL_REGEX, href.lower())
                if url_volume_match:
                    volume_num = f"{url_volume_match.group(1)}.{url_volume_match.group(2)}"
                else:
                    # Si aún no se encuentra, intentar un patrón más general que podría capturar otros formatos
                    url_volume_match = re.search(VOLUME_GENERAL_DECIMAL_REGEX, href.lower())
                    if url_volume_match:
                        volume_num = f"{url_volume_match.group(1)}.{url_volume_match.group(2)}"
                    else:
                        volume_num = "1"  # Volumen predeterminado
            else:
                volume_num = url_volume_match.group(1)

            chapter_links.append({
                'text': text,
                'href': href,
                'volume': volume_num,
                'chapter': chapter_num,
                'part': part_num
            })

    # Organizar capítulos por volumen y número de capítulo
    volumes = defaultdict(lambda: defaultdict(list))

    for link in chapter_links:
        # Extraer el número de volumen directamente de la URL para asegurar una separación adecuada
        url_volume_match = re.search(VOLUME_DECIMAL_REGEX, link['href'].lower())
        if url_volume_match:
            # Usar el número de volumen exacto de la URL
            exact_volume_num = url_volume_match.group(1)
            volumes[exact_volume_num][link['chapter']].append({
                'part': link['part'],
                'title': link['text'],
                'url': link['href']
            })
        else:
            # Recurrir al número de volumen extraído previamente
            volumes[link['volume']][link['chapter']].append({
                'part': link['part'],
                'title': link['text'],
                'url': link['href']
            })

    # Convertir defaultdict a dict regular para serialización JSON
    result = {}
    for vol_num, chapters in volumes.items():
        result[vol_num] = {}
        for chap_num, parts in chapters.items():
            # Ordenar partes por número de parte
            sorted_parts = sorted(parts, key=lambda x: int(x['part']))
            result[vol_num][chap_num] = sorted_parts

    # También encontrar prólogos, epílogos e historias cortas
    special_sections = []
    for link in soup.find_all('a'):
        text = link.get_text(strip=True)
        href = link.get('href')

        if not (text and href):
            continue

        # Verificar prólogos, epílogos e historias cortas
        is_prologue = 'prólogo' in text.lower() or 'prologo' in text.lower()
        is_epilogue = 'epílogo' in text.lower() or 'epilogo' in text.lower()
        is_short_story = 'historias cortas' in text.lower() or 'historia corta' in text.lower() or ' ss ' in f" {text.lower()} "

        if is_prologue or is_epilogue or is_short_story:
            # Extraer número de volumen de la URL
            # Primero, verificar el formato decimal explícito (ej: volumen-4.5)
            url_volume_match = re.search(VOLUME_DECIMAL_REGEX, href.lower())

            # Si no se encuentra, verificar el formato con guiones (ej: volumen-4-5)
            if not url_volume_match:
                url_volume_match = re.search(VOLUME_HYPHEN_DECIMAL_REGEX, href.lower())
                if url_volume_match:
                    volume_num = f"{url_volume_match.group(1)}.{url_volume_match.group(2)}"
                else:
                    # Si aún no se encuentra, intentar un patrón más general que podría capturar otros formatos
                    url_volume_match = re.search(r'volumen[\s\-_]*(\d+)[\s\-_]*(\d+)', href.lower())
                    if url_volume_match:
                        volume_num = f"{url_volume_match.group(1)}.{url_volume_match.group(2)}"
                    else:
                        volume_num = "1"  # Volumen predeterminado
            else:
                volume_num = url_volume_match.group(1)

            # Determinar tipo de sección
            if is_prologue:
                section_type = 'prologue'
            elif is_epilogue:
                section_type = 'epilogue'
            else:
                section_type = 'short_story'

            special_sections.append({
                'text': text,
                'href': href,
                'volume': volume_num,
                'type': section_type
            })

    # Organizar secciones especiales por volumen y tipo
    for section in special_sections:
        # Extraer el número de volumen directamente de la URL para asegurar una separación adecuada
        # Primero, verificar el formato decimal explícito (ej: volumen-4.5)
        url_volume_match = re.search(VOLUME_DECIMAL_REGEX, section['href'].lower())

        # Si no se encuentra, verificar el formato con guiones (ej: volumen-4-5)
        if not url_volume_match:
            url_volume_match = re.search(VOLUME_HYPHEN_DECIMAL_REGEX, section['href'].lower())
            if url_volume_match:
                volume_num = f"{url_volume_match.group(1)}.{url_volume_match.group(2)}"
            else:
                # Si aún no se encuentra, intentar un patrón más general que podría capturar otros formatos
                url_volume_match = re.search(VOLUME_GENERAL_DECIMAL_REGEX, section['href'].lower())
                if url_volume_match:
                    volume_num = f"{url_volume_match.group(1)}.{url_volume_match.group(2)}"
                else:
                    volume_num = section['volume']
        else:
            volume_num = url_volume_match.group(1)

        section_type = section['type']

        # Omitir entradas con URLs de pestañas (entradas inválidas)
        if TAB_URL_PREFIX in section['href']:
            continue

        # Asegurarse de que el volumen exista en el resultado
        if volume_num not in result:
            result[volume_num] = {}

        if section_type == 'prologue':
            if 'prologue' not in result[volume_num]:
                result[volume_num]['prologue'] = []
            result[volume_num]['prologue'].append({
                'title': section['text'],
                'url': section['href']
            })
        elif section_type == 'epilogue':
            if 'epilogue' not in result[volume_num]:
                result[volume_num]['epilogue'] = []
            result[volume_num]['epilogue'].append({
                'title': section['text'],
                'url': section['href']
            })
        elif section_type == 'short_story':
            if 'short_story' not in result[volume_num]:
                result[volume_num]['short_story'] = []
            # Omitir entradas irrelevantes como "See how your comment data is processed"
            if not ('see how your comment' in section['text'].lower() or 'privacy' in section['text'].lower()):
                result[volume_num]['short_story'].append({
                    'title': section['text'],
                    'url': section['href']
                })

    # Función para separar manualmente volúmenes decimales de sus contrapartes enteras
    def separate_decimal_volumes(result, base_vol, decimal_vol):
        if base_vol in result:
            if decimal_vol not in result:
                result[decimal_vol] = {'prologue': [], 'epilogue': [], 'short_story': []}

            # Mover contenido de volumen decimal desde volumen base
            for chapter_num in list(result[base_vol].keys()):
                if chapter_num.isdigit():
                    # Verificar si alguna parte en este capítulo es del volumen decimal
                    decimal_vol_parts = []
                    base_vol_parts = []

                    for part in result[base_vol][chapter_num]:
                        # Verificar ambos formatos: volumen-4.5 y volumen-4-5
                        decimal_vol_in_url = False
                        url = part['url'].lower()

                        # Verificar formato decimal (ej: volumen-4.5)
                        decimal_pattern = f'volumen-{decimal_vol}'
                        if decimal_pattern in url:
                            decimal_vol_in_url = True

                        # Verificar formato con guiones (ej: volumen-4-5)
                        hyphenated_decimal = decimal_vol.replace(".", "-")
                        hyphenated_pattern = f'volumen-{hyphenated_decimal}'
                        if hyphenated_pattern in url:
                            decimal_vol_in_url = True

                        # Verificar patrón más general, pero solo si la parte decimal es válida
                        vol_parts = decimal_vol.split('.')
                        if len(vol_parts) == 2 and is_valid_decimal_part(vol_parts[1]):
                            general_pattern = f'volumen.*{vol_parts[0]}.*{vol_parts[1]}'
                            if re.search(general_pattern, url):
                                decimal_vol_in_url = True

                        if decimal_vol_in_url:
                            decimal_vol_parts.append(part)
                        else:
                            base_vol_parts.append(part)

                    # Si encontramos partes de volumen decimal, moverlas
                    if decimal_vol_parts:
                        if chapter_num not in result[decimal_vol]:
                            result[decimal_vol][chapter_num] = []
                        result[decimal_vol][chapter_num] = decimal_vol_parts

                        # Actualizar volumen base para que solo contenga partes de volumen base
                        if base_vol_parts:
                            result[base_vol][chapter_num] = base_vol_parts
                        else:
                            del result[base_vol][chapter_num]

            # Mover secciones especiales
            for section_type in ['prologue', 'epilogue', 'short_story']:
                if section_type in result[base_vol]:
                    decimal_vol_sections = []
                    base_vol_sections = []

                    for section in result[base_vol][section_type]:
                        # Verificar ambos formatos: volumen-4.5 y volumen-4-5
                        decimal_vol_in_url = False
                        url = section['url'].lower()

                        # Verificar formato decimal (ej: volumen-4.5)
                        decimal_pattern = f'volumen-{decimal_vol}'
                        if decimal_pattern in url:
                            decimal_vol_in_url = True

                        # Verificar formato con guiones (ej: volumen-4-5)
                        hyphenated_decimal = decimal_vol.replace(".", "-")
                        hyphenated_pattern = f'volumen-{hyphenated_decimal}'
                        if hyphenated_pattern in url:
                            decimal_vol_in_url = True

                        # Verificar patrón más general, pero solo si la parte decimal es válida
                        vol_parts = decimal_vol.split('.')
                        if len(vol_parts) == 2 and is_valid_decimal_part(vol_parts[1]):
                            general_pattern = f'volumen.*{vol_parts[0]}.*{vol_parts[1]}'
                            if re.search(general_pattern, url):
                                decimal_vol_in_url = True

                        if decimal_vol_in_url:
                            decimal_vol_sections.append(section)
                        else:
                            base_vol_sections.append(section)

                    # Si encontramos secciones de volumen decimal, moverlas
                    if decimal_vol_sections:
                        result[decimal_vol][section_type] = decimal_vol_sections

                        # Actualizar volumen base para que solo contenga secciones de volumen base
                        if base_vol_sections:
                            result[base_vol][section_type] = base_vol_sections
                        else:
                            del result[base_vol][section_type]

    # Detectar automáticamente y separar volúmenes decimales de sus contrapartes enteras
    # Encontrar todos los volúmenes decimales en el resultado
    decimal_volumes = [vol for vol in result.keys() if '.' in vol]

    # Verificar cualquier volumen decimal faltante que pueda estar presente en URLs
    all_urls = [part['url'] for vol in result.values() for chap in vol.values() if isinstance(chap, list) for part in chap]

    # Extraer todos los volúmenes decimales potenciales de URLs
    potential_decimal_volumes = set()

    # Patrones más específicos para encontrar volúmenes decimales en URLs
    # Estamos buscando patrones como 'volumen-4-5' o 'volumen-4.5' donde el segundo número es específicamente una parte decimal

    # Patrón para formato con guiones (ej: volumen-4-5)
    decimal_vol_pattern = re.compile(r'volumen[\s\-_]*(\d+)[\s\-_]*(\d+)')

    # Patrón para formato decimal (ej: volumen-4.5)
    decimal_vol_pattern2 = re.compile(r'volumen[\s\-_]*(\d+)\.(\d+)')

    for url in all_urls:
        url_lower = url.lower()

        # Verificar formato con guiones (ej: volumen-4-5)
        matches = decimal_vol_pattern.findall(url_lower)
        for match in matches:
            if len(match) == 2 and is_valid_decimal_part(match[1]):
                decimal_vol = f"{match[0]}.{match[1]}"
                potential_decimal_volumes.add(decimal_vol)

        # Verificar formato decimal (ej: volumen-4.5)
        matches = decimal_vol_pattern2.findall(url_lower)
        for match in matches:
            if len(match) == 2 and is_valid_decimal_part(match[1]):
                decimal_vol = f"{match[0]}.{match[1]}"
                potential_decimal_volumes.add(decimal_vol)

    # También verificar volúmenes decimales en títulos con patrones más específicos
    title_decimal_vol_pattern = re.compile(r'volumen[\s\-_]*(\d+)[\.,](\d+)')
    title_decimal_vol_pattern2 = re.compile(r'vol(?:\.|umen)?[\s\-_]*(\d+)[\.,](\d+)')

    for vol in result.values():
        for _, parts in vol.items():  # Usar _ para variable no utilizada
            if not isinstance(parts, list):
                continue
            for part in parts:
                title_lower = part['title'].lower()

                # Verificar volúmenes decimales en títulos
                for pattern in [title_decimal_vol_pattern, title_decimal_vol_pattern2]:
                    matches = pattern.findall(title_lower)
                    for match in matches:
                        if len(match) == 2 and is_valid_decimal_part(match[1]):
                            decimal_vol = f"{match[0]}.{match[1]}"
                            potential_decimal_volumes.add(decimal_vol)

    # Agregar cualquier volumen decimal faltante que se haya encontrado
    for decimal_vol in potential_decimal_volumes:
        if decimal_vol not in decimal_volumes and decimal_vol.split('.')[0] in result:
            print(f"\nSe detectó un volumen decimal potencial {decimal_vol} de URLs/títulos que no se encontró automáticamente")
            decimal_volumes.append(decimal_vol)

    # Procesar cada volumen decimal
    for decimal_vol in decimal_volumes:
        base_vol = decimal_vol.split('.')[0]
        separate_decimal_volumes(result, base_vol, decimal_vol)

    # Verificación final para cualquier volumen decimal faltante
    # Esta es una red de seguridad para asegurarse de que no nos perdamos ningún volumen decimal
    for potential_decimal_vol in potential_decimal_volumes:
        vol_parts = potential_decimal_vol.split('.')
        if len(vol_parts) != 2 or not is_valid_decimal_part(vol_parts[1]):
            continue  # Omitir si no es un volumen decimal válido

        base_vol = vol_parts[0]

        # Si el volumen decimal todavía no está en el resultado pero su volumen base sí
        if potential_decimal_vol not in result and base_vol in result:
            print(f"\nVerificación final: Volumen {potential_decimal_vol} aún no detectado. Creándolo...")
            # Crear una estructura vacía para el volumen decimal
            result[potential_decimal_vol] = {'prologue': [], 'epilogue': [], 'short_story': []}

            # Intentar encontrar cualquier contenido que pueda pertenecer a este volumen decimal en su volumen base
            for chapter_num in list(result[base_vol].keys()):
                if chapter_num.isdigit():
                    # Buscar cualquier parte que pueda ser de este volumen decimal basado en título o URL
                    decimal_vol_parts = []
                    for part in result[base_vol][chapter_num]:
                        title_lower = part['title'].lower()
                        url_lower = part['url'].lower()

                        # Verificar si el título o URL contiene referencias a este volumen decimal
                        decimal_in_title = f"volumen {potential_decimal_vol}" in title_lower or f"vol {potential_decimal_vol}" in title_lower
                        decimal_in_url = f"volumen-{potential_decimal_vol}" in url_lower or f"volumen-{potential_decimal_vol.replace('.', '-')}" in url_lower

                        if decimal_in_title or decimal_in_url:
                            decimal_vol_parts.append(part)

                    # Si encontramos alguna parte, moverla al volumen decimal
                    if decimal_vol_parts:
                        if chapter_num not in result[potential_decimal_vol]:
                            result[potential_decimal_vol][chapter_num] = []
                        result[potential_decimal_vol][chapter_num].extend(decimal_vol_parts)
                        print(f"  Se movieron {len(decimal_vol_parts)} partes del Capítulo {chapter_num} al Volumen {potential_decimal_vol}")

    # Crear una estructura más completa para una visualización más fácil
    comprehensive_structure = []
    for vol_num in sorted(result.keys(), key=lambda x: float(x)):
        # Verificar si este volumen tiene algún contenido (capítulos o secciones especiales)
        has_chapters = any(k.isdigit() for k in result[vol_num].keys())
        has_prologue = 'prologue' in result[vol_num] and result[vol_num]['prologue']
        has_epilogue = 'epilogue' in result[vol_num] and result[vol_num]['epilogue']
        has_short_story = 'short_story' in result[vol_num] and result[vol_num]['short_story']

        # Solo incluir volúmenes que tengan contenido real
        if has_chapters or has_prologue or has_epilogue or has_short_story:
            volume = {
                'title': f"Volumen {vol_num}",
                'content': []
            }

            # Siempre agregar secciones en el orden correcto: prólogo, capítulos, epílogo, historias cortas

            # Agregar prólogo si existe
            if has_prologue:
                # Filtrar entradas inválidas con URLs de pestañas
                valid_prologues = [p for p in result[vol_num]['prologue'] if not p['url'].startswith(TAB_URL_PREFIX)]
                if valid_prologues:
                    prologue_section = {
                        'title': 'Prólogo',
                        'type': 'prologue',
                        'parts': valid_prologues
                    }
                    volume['content'].append(prologue_section)

            # Agregar capítulos
            for chap_num in sorted([k for k in result[vol_num].keys() if k.isdigit()], key=lambda x: int(x)):
                chapter = {
                    'title': f"Capítulo {chap_num}",
                    'type': 'chapter',
                    'parts': result[vol_num][chap_num]
                }
                volume['content'].append(chapter)

            # Agregar epílogo si existe
            if has_epilogue:
                # Filtrar entradas inválidas con URLs de pestañas
                valid_epilogues = [e for e in result[vol_num]['epilogue'] if not e['url'].startswith(TAB_URL_PREFIX)]
                if valid_epilogues:
                    epilogue_section = {
                        'title': 'Epílogo',
                        'type': 'epilogue',
                        'parts': valid_epilogues
                    }
                    volume['content'].append(epilogue_section)

            # Agregar historias cortas si existen
            if has_short_story:
                # Filtrar entradas inválidas con URLs de pestañas
                valid_ss = [ss for ss in result[vol_num]['short_story'] if not ss['url'].startswith(TAB_URL_PREFIX)]
                if valid_ss:
                    ss_section = {
                        'title': 'Historias Cortas (SS)',
                        'type': 'short_story',
                        'parts': valid_ss
                    }
                    volume['content'].append(ss_section)

            # Solo agregar el volumen si tiene contenido
            if volume['content']:
                comprehensive_structure.append(volume)

    # Extraer URLs de portadas
    cover_urls = extract_cover_images(html_content, url)

    # Crear una salida final con metadatos
    final_output = {
        "metadata": {
            "generated_at": current_datetime,
            "title": novel_title,
            "total_volumes": len(result),
            "total_chapters": sum(len([k for k in chapters.keys() if k.isdigit()]) for chapters in result.values()),
            "total_parts": sum(len(parts) for chapters in result.values() for key, parts in chapters.items() if key.isdigit())
        },
        "volumes": comprehensive_structure
    }

    # Añadir portadas a la estructura
    final_output = add_covers_to_structure(final_output, cover_urls, novel_title, scraper_instance)

    return final_output, result
